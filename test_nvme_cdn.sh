
#!/bin/bash

# =================================================================
# KSYUN NVMe SSD 测试脚本 
# =================================================================

# --- 可配置参数 ---
# !!! 警告：请将此设备更改为您要测试的、可被完全擦除数据的 NVMe 硬盘设备名 !!!
TARGET_DEVICE="/dev/nvme1n1"

# 测试结果输出目录
OUTPUT_DIR="./fio_results_$(date +%Y%m%d_%H%M%S)"

# --- 第1阶段: 预写配置 ---
PRECONDITION_BS="2M"
PRECONDITION_RUNTIME="1800s" # 30 分钟

# --- 第2阶段: 性能测试配置 ---
# 要测试的块大小列表
BLOCK_SIZES=("4k" "128k" "1M")
# 测试模型: 80% 随机读, 20% 随机写
RW_MIX_READ_PERCENT=80
# 单项测试的运行时长（秒）
RUNTIME=3600 # 1小时
# 测试开始前的预热时间（秒）
RAMP_TIME=30
# 【根据您的CPU调整】并发线程数 (56线程CPU)
NUM_JOBS=56
# 【根据您的CPU调整】每个线程的队列深度 (总队列深度 ≈ 504)
IODEPTH_PER_JOB=9
# 性能日志记录间隔（毫秒）
LOG_INTERVAL=5000 # 5 秒


# --- 脚本主体 ---

# 检查目标设备是否存在
if [ ! -b "$TARGET_DEVICE" ]; then
    echo "错误: 设备 $TARGET_DEVICE 不存在或不是一个块设备。"
    echo "请修改脚本中的 TARGET_DEVICE 变量。"
    exit 1
fi

#【关键安全检查】确认操作
echo "警告：本测试将对设备 $TARGET_DEVICE 进行破坏性读写！"
echo "整个过程包括30分钟的预写和多轮测试，耗时较长。"
echo "设备上的所有数据都将丢失！"
read -p "请输入 'yes' 以确认继续: " confirm
if [ "$confirm" != "yes" ]; then
    echo "操作已取消。"
    exit 0
fi

# 创建结果目录
mkdir -p "$OUTPUT_DIR"
echo "测试结果将保存在: $OUTPUT_DIR"
echo "---------------------------------------------------------"


# =================================================================
# 阶段 1: 硬盘预写 (Pre-conditioning)
# =================================================================
echo "[阶段 1/3] 开始进行硬盘预写，让其进入稳定态..."
echo "  - 块大小: $PRECONDITION_BS"
echo "  - 模式: 顺序写"
echo "  - 时长: $PRECONDITION_RUNTIME (30 分钟)"

sudo fio --name=precondition \
    --filename=$TARGET_DEVICE \
    --direct=1 \
    --ioengine=libaio \
    --rw=write \
    --bs=$PRECONDITION_BS \
    --runtime=$PRECONDITION_RUNTIME \
    --time_based \
    --numjobs=1 \
    --iodepth=16

echo "[阶段 1/3] 硬盘预写完成。等待10秒让主控稳定..."
echo "---------------------------------------------------------"
sleep 10


# =================================================================
# 阶段 2: 核心性能基准测试
# =================================================================
echo "[阶段 2/3] 开始进行核心性能基准测试..."
# 通用 fio 参数
FIO_BASE_OPTS="--filename=$TARGET_DEVICE --direct=1 --ioengine=libaio --group_reporting --time_based --norandommap"
FIO_WORKLOAD_OPTS="--rw=randrw --rwmixread=$RW_MIX_READ_PERCENT --numjobs=$NUM_JOBS --iodepth=$IODEPTH_PER_JOB --runtime=$RUNTIME --ramp_time=$RAMP_TIME"

for bs in "${BLOCK_SIZES[@]}"; do
    echo "--> 正在测试: 80/20 混合随机IO, 块大小: $bs"
    
    LOG_PREFIX="$OUTPUT_DIR/mixed_rw_${bs}"
    
    sudo fio --name="mixed_rw_${bs}" \
        ${FIO_BASE_OPTS} \
        ${FIO_WORKLOAD_OPTS} \
        --bs=$bs \
        --write_bw_log="${LOG_PREFIX}" \
        --write_iops_log="${LOG_PREFIX}" \
        --log_avg_msec=$LOG_INTERVAL \
        --output="${LOG_PREFIX}_summary.txt"

    echo "--> 测试完成: 块大小 $bs"
    echo "---------------------------------------------------------"
    sleep 5
done
echo "[阶段 2/3] 所有基准测试完成。"


# =================================================================
# 阶段 3: 生成性能图表
# =================================================================
echo "[阶段 3/3] 开始生成性能曲线图表 (PNG格式)..."

# 使用趋势线图表生成器
CHART_SCRIPT="$(dirname "$0")/fio-web-controller/generate_trend_charts.sh"
if [ -f "$CHART_SCRIPT" ]; then
    echo "使用趋势线图表生成器..."
    bash "$CHART_SCRIPT" "$OUTPUT_DIR"
else
    echo "未找到改进的图表生成器，使用原始方法..."
    for bs in "${BLOCK_SIZES[@]}"; do
        LOG_PREFIX="$OUTPUT_DIR/mixed_rw_${bs}"
        CHART_FILE="${LOG_PREFIX}_bw_chart.png"
        # 【已修正】指定正确的日志文件名 (fio-3.1 会生成 _bw.1.log, _bw.2.log 等)
        # 我们使用第一个 job 的日志作为绘图样本
        SOURCE_LOG_FILE="${LOG_PREFIX}_bw.1.log"

        # 检查日志文件是否存在
        if [ ! -f "${SOURCE_LOG_FILE}" ]; then
            echo "警告: 找不到日志文件 ${SOURCE_LOG_FILE}，跳过块大小为 ${bs} 的图表生成。"
            continue
        fi

        # 【已修正】使用 gnuplot 从同一个日志文件的不同列读取数据来生成图表
        gnuplot -e "
            set terminal pngcairo size 1200,600 font 'Verdana,10';
            set output '${CHART_FILE}';
            set title 'NVMe Performance (80% Read / 20% Write, Block Size: ${bs})';
            set xlabel 'Time (seconds)';
            set ylabel 'Bandwidth (MB/s)';
            set grid;
            set key top right;
            set format x '%g';

            plot '${SOURCE_LOG_FILE}' using (\$1/1000):(\$2/1024) with lines title 'Read BW (MB/s)', \
                 '${SOURCE_LOG_FILE}' using (\$1/1000):(\$3/1024) with lines title 'Write BW (MB/s)';
        "
        echo "--> 图表已生成: ${CHART_FILE}"
    done
fi

echo "[阶段 3/3] 图表生成完成。"
echo "========================================================="
# 【已修正】修复了此处的语法错误
echo "所有测试已全部完成！"
echo "详细的摘要和日志数据保存在目录: $OUTPUT_DIR"
