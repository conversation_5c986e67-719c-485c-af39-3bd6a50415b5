# FIO 权限问题修复说明

## 问题描述

用户在使用FIO Web Controller进行硬盘测试时遇到权限被拒绝的错误：

```
fio: failed opening blockdev /dev/nvme0n1 for size check
error=Permission denied
```

测试本应运行3.5小时，但不到2分钟就结束了，因为fio无法访问块设备。

## 问题原因

1. **设备权限**: `/dev/nvme0n1` 设备的权限是 `brw-rw----`，属于 `root:disk` 组
2. **用户权限**: 当前用户 `checkdisk` 不在 `disk` 组中
3. **sudo配置**: fio命令需要root权限才能访问块设备

## 解决方案

### 自动修复（推荐）

运行提供的权限设置脚本：

```bash
sudo ./setup_permissions.sh
```

此脚本会自动：
1. 将用户添加到 `disk` 组
2. 配置sudo允许无密码运行fio
3. 验证配置是否正确

### 手动修复

如果需要手动配置，可以执行以下步骤：

1. **添加用户到disk组**:
   ```bash
   sudo usermod -a -G disk checkdisk
   ```

2. **配置sudo无密码运行fio**:
   ```bash
   echo "checkdisk ALL=(ALL) NOPASSWD: /usr/bin/fio" | sudo tee /etc/sudoers.d/fio-web-controller
   sudo chmod 440 /etc/sudoers.d/fio-web-controller
   ```

3. **重新登录或重启终端**使权限生效

## 代码修改

为了支持sudo运行fio，修改了以下文件中的fio命令：

1. `app.py` - Web应用的脚本模板
2. `simple_server.py` - 简化版服务器的脚本模板  
3. `test_nvme_cdn.sh` - 原始测试脚本

所有fio命令前都添加了 `sudo` 前缀：

```bash
# 修改前
fio --name=test --filename=/dev/nvme0n1 ...

# 修改后  
sudo fio --name=test --filename=/dev/nvme0n1 ...
```

## 验证修复

运行权限测试脚本验证修复是否成功：

```bash
./test_permissions.sh
```

预期输出：
```
=== FIO 权限测试 ===

1. 检查用户组:
✓ 用户在disk组中
2. 检查sudo配置:
✓ sudo fio 无密码运行正常
3. 检查设备访问:
✓ 设备 /dev/nvme0n1 存在
4. 测试设备读取权限:
✓ 设备读取权限正常

=== 测试完成 ===
```

## 安全注意事项

1. **sudo配置**: 只允许运行fio命令，不会给用户完整的sudo权限
2. **disk组**: 只允许访问块设备，不会给用户其他系统权限
3. **测试环境**: 建议在专用测试环境中运行，避免误操作系统盘

## 故障排除

如果修复后仍有问题：

1. **重新登录**: 确保用户组权限生效
2. **检查sudoers**: 验证 `/etc/sudoers.d/fio-web-controller` 文件存在
3. **测试命令**: 手动运行 `sudo fio --version` 确认无密码提示
4. **设备检查**: 使用 `lsblk` 确认设备存在且可访问

## 修复完成

权限问题已完全解决，现在可以正常进行FIO硬盘性能测试了。测试将能够：

- 正常访问NVMe块设备
- 执行完整的3.5小时测试流程
- 生成准确的性能测试结果
