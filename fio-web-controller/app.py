#!/usr/bin/env python3
"""
FIO NVMe 测试脚本 Web 控制器
使用 Flask 提供 Web 界面来控制 fio 硬盘测试脚本
"""

import os
import subprocess
import threading
import time
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import signal
import sys

app = Flask(__name__)
app.config['SECRET_KEY'] = 'fio-web-controller-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
current_process = None
test_status = {
    'running': False,
    'stage': '',
    'progress': 0,
    'start_time': None,
    'logs': []
}

class TestRunner:
    def __init__(self):
        self.process = None
        self.thread = None
        
    def run_test(self, config):
        """运行 fio 测试"""
        global test_status
        
        # 重置状态
        test_status = {
            'running': True,
            'stage': '准备测试',
            'progress': 0,
            'start_time': datetime.now().isoformat(),
            'logs': []
        }
        
        # 创建修改后的脚本
        script_content = self.generate_script(config)
        script_path = '/tmp/fio_test_custom.sh'
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        # 在新线程中运行脚本
        self.thread = threading.Thread(target=self._execute_script, args=(script_path,))
        self.thread.start()
        
    def _execute_script(self, script_path):
        """在后台执行脚本"""
        global test_status
        
        try:
            # 启动脚本进程
            self.process = subprocess.Popen(
                ['/bin/bash', script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时读取输出
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    log_entry = {
                        'timestamp': datetime.now().isoformat(),
                        'message': line.strip()
                    }
                    test_status['logs'].append(log_entry)
                    
                    # 分析进度
                    self._analyze_progress(line.strip())
                    
                    # 发送实时更新
                    socketio.emit('test_update', test_status)
                    
                    # 限制日志数量
                    if len(test_status['logs']) > 1000:
                        test_status['logs'] = test_status['logs'][-500:]
            
            # 等待进程完成
            self.process.wait()
            
            if self.process.returncode == 0:
                test_status['stage'] = '测试完成'
                test_status['progress'] = 100
            else:
                test_status['stage'] = '测试失败'
                
        except Exception as e:
            test_status['stage'] = f'错误: {str(e)}'
            test_status['logs'].append({
                'timestamp': datetime.now().isoformat(),
                'message': f'执行错误: {str(e)}'
            })
        finally:
            test_status['running'] = False
            socketio.emit('test_update', test_status)
            
    def _analyze_progress(self, line):
        """分析日志行来更新进度"""
        global test_status
        
        if '阶段 1/3' in line:
            test_status['stage'] = '硬盘预写'
            test_status['progress'] = 10
        elif '阶段 2/3' in line:
            test_status['stage'] = '性能测试'
            test_status['progress'] = 40
        elif '阶段 3/3' in line:
            test_status['stage'] = '生成图表'
            test_status['progress'] = 80
        elif '所有测试已全部完成' in line:
            test_status['stage'] = '测试完成'
            test_status['progress'] = 100
            
    def stop_test(self):
        """停止测试"""
        global test_status
        
        if self.process and self.process.poll() is None:
            self.process.terminate()
            time.sleep(2)
            if self.process.poll() is None:
                self.process.kill()
                
        test_status['running'] = False
        test_status['stage'] = '测试已停止'
        
    def generate_script(self, config):
        """根据配置生成脚本内容"""
        script_template = '''#!/bin/bash

# FIO NVMe 测试脚本 - Web控制版本

TARGET_DEVICE="{target_device}"
OUTPUT_DIR="./fio_results_$(date +%Y%m%d_%H%M%S)"

# 预写配置
PRECONDITION_BS="{precondition_bs}"
PRECONDITION_RUNTIME="{precondition_runtime}"

# 性能测试配置
BLOCK_SIZES=({block_sizes})
RW_MIX_READ_PERCENT={rw_mix_read_percent}
RUNTIME={runtime}
RAMP_TIME={ramp_time}
NUM_JOBS={num_jobs}
IODEPTH_PER_JOB={iodepth_per_job}
LOG_INTERVAL={log_interval}

# 检查目标设备
if [ ! -b "$TARGET_DEVICE" ]; then
    echo "错误: 设备 $TARGET_DEVICE 不存在或不是一个块设备。"
    exit 1
fi

# 创建结果目录
mkdir -p "$OUTPUT_DIR"
echo "测试结果将保存在: $OUTPUT_DIR"
echo "---------------------------------------------------------"

# 阶段 1: 硬盘预写
echo "[阶段 1/3] 开始进行硬盘预写，让其进入稳定态..."
echo "  - 块大小: $PRECONDITION_BS"
echo "  - 模式: 顺序写"
echo "  - 时长: $PRECONDITION_RUNTIME"

sudo fio --name=precondition \\
    --filename=$TARGET_DEVICE \\
    --direct=1 \\
    --ioengine=libaio \\
    --rw=write \\
    --bs=$PRECONDITION_BS \\
    --runtime=$PRECONDITION_RUNTIME \\
    --time_based \\
    --numjobs=1 \\
    --iodepth=16

echo "[阶段 1/3] 硬盘预写完成。等待10秒让主控稳定..."
sleep 10

# 阶段 2: 核心性能基准测试
echo "[阶段 2/3] 开始进行核心性能基准测试..."
FIO_BASE_OPTS="--filename=$TARGET_DEVICE --direct=1 --ioengine=libaio --group_reporting --time_based --norandommap"
FIO_WORKLOAD_OPTS="--rw=randrw --rwmixread=$RW_MIX_READ_PERCENT --numjobs=$NUM_JOBS --iodepth=$IODEPTH_PER_JOB --runtime=$RUNTIME --ramp_time=$RAMP_TIME"

for bs in "${{BLOCK_SIZES[@]}}"; do
    echo "--> 正在测试: 80/20 混合随机IO, 块大小: $bs"
    
    LOG_PREFIX="$OUTPUT_DIR/mixed_rw_${{bs}}"
    
    sudo fio --name="mixed_rw_${{bs}}" \\
        ${{FIO_BASE_OPTS}} \\
        ${{FIO_WORKLOAD_OPTS}} \\
        --bs=$bs \\
        --write_bw_log="${{LOG_PREFIX}}" \\
        --write_iops_log="${{LOG_PREFIX}}" \\
        --log_avg_msec=$LOG_INTERVAL \\
        --output="${{LOG_PREFIX}}_summary.txt"

    echo "--> 测试完成: 块大小 $bs"
    sleep 5
done

echo "[阶段 2/3] 所有基准测试完成。"

# 阶段 3: 生成性能图表
echo "[阶段 3/3] 开始生成性能曲线图表..."

# 使用趋势线图表生成器
CHART_SCRIPT="$(dirname "$0")/generate_trend_charts.sh"
if [ -f "$CHART_SCRIPT" ]; then
    echo "使用趋势线图表生成器..."
    bash "$CHART_SCRIPT" "$OUTPUT_DIR"
else
    echo "未找到改进的图表生成器，使用原始方法..."
    for bs in "${{BLOCK_SIZES[@]}}"; do
        LOG_PREFIX="$OUTPUT_DIR/mixed_rw_${{bs}}"
        CHART_FILE="${{LOG_PREFIX}}_bw_chart.png"
        SOURCE_LOG_FILE="${{LOG_PREFIX}}_bw.1.log"

        if [ ! -f "${{SOURCE_LOG_FILE}}" ]; then
            echo "警告: 找不到日志文件 ${{SOURCE_LOG_FILE}}"
            continue
        fi

        if command -v gnuplot >/dev/null 2>&1; then
            gnuplot -e "
                set terminal pngcairo size 1200,600 font 'Verdana,10';
                set output '${{CHART_FILE}}';
                set title 'NVMe Performance (80% Read / 20% Write, Block Size: ${{bs}})';
                set xlabel 'Time (seconds)';
                set ylabel 'Bandwidth (MB/s)';
                set grid;
                set key top right;
                set format x '%g';

                plot '${{SOURCE_LOG_FILE}}' using (\\$1/1000):(\\$2/1024) with lines title 'Read BW (MB/s)', \\
                     '${{SOURCE_LOG_FILE}}' using (\\$1/1000):(\\$3/1024) with lines title 'Write BW (MB/s)';
            "
            echo "--> 图表已生成: ${{CHART_FILE}}"
        else
            echo "警告: gnuplot 未安装，跳过图表生成"
        fi
    done
fi

echo "[阶段 3/3] 图表生成完成。"
echo "所有测试已全部完成！"
echo "详细的摘要和日志数据保存在目录: $OUTPUT_DIR"
'''.format(
            target_device=config.get('target_device', '/dev/nvme1n1'),
            precondition_bs=config.get('precondition_bs', '2M'),
            precondition_runtime=config.get('precondition_runtime', '1800s'),
            block_sizes=' '.join([f'"{bs}"' for bs in config.get('block_sizes', ['4k', '128k', '1M'])]),
            rw_mix_read_percent=config.get('rw_mix_read_percent', 80),
            runtime=config.get('runtime', 3600),
            ramp_time=config.get('ramp_time', 30),
            num_jobs=config.get('num_jobs', 56),
            iodepth_per_job=config.get('iodepth_per_job', 9),
            log_interval=config.get('log_interval', 5000)
        )
        
        return script_template

# 创建测试运行器实例
test_runner = TestRunner()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/config', methods=['GET'])
def get_config():
    """获取默认配置"""
    default_config = {
        'target_device': '/dev/nvme1n1',
        'precondition_bs': '2M',
        'precondition_runtime': '1800s',
        'block_sizes': ['4k', '128k', '1M'],
        'rw_mix_read_percent': 80,
        'runtime': 3600,
        'ramp_time': 30,
        'num_jobs': 56,
        'iodepth_per_job': 9,
        'log_interval': 5000
    }
    return jsonify(default_config)

@app.route('/api/start', methods=['POST'])
def start_test():
    """启动测试"""
    global test_status
    
    if test_status['running']:
        return jsonify({'error': '测试正在运行中'}), 400
        
    config = request.json
    test_runner.run_test(config)
    
    return jsonify({'message': '测试已启动'})

@app.route('/api/stop', methods=['POST'])
def stop_test():
    """停止测试"""
    test_runner.stop_test()
    return jsonify({'message': '测试已停止'})

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取测试状态"""
    return jsonify(test_status)

@app.route('/api/devices', methods=['GET'])
def get_devices():
    """获取可用的块设备列表"""
    try:
        result = subprocess.run(['lsblk', '-d', '-n', '-o', 'NAME,SIZE,TYPE'], 
                              capture_output=True, text=True)
        devices = []
        for line in result.stdout.strip().split('\n'):
            if line and 'disk' in line:
                parts = line.split()
                if len(parts) >= 3:
                    devices.append({
                        'name': f'/dev/{parts[0]}',
                        'size': parts[1],
                        'type': parts[2]
                    })
        return jsonify(devices)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """WebSocket 连接"""
    emit('test_update', test_status)

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket 断开连接"""
    pass

def signal_handler(sig, frame):
    """信号处理器"""
    print('正在关闭服务器...')
    if test_runner.process:
        test_runner.stop_test()
    sys.exit(0)

if __name__ == '__main__':
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("FIO Web Controller 启动中...")
    print("访问 http://0.0.0.0:5000 来使用Web界面")
    print("按 Ctrl+C 停止服务器")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
