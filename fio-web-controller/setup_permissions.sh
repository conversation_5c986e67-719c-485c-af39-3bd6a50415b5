#!/bin/bash

# FIO Web Controller 权限设置脚本

echo "=== FIO Web Controller 权限设置 ==="
echo

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误: 此脚本需要root权限运行"
    echo "请使用: sudo ./setup_permissions.sh"
    exit 1
fi

# 获取当前用户名（如果通过sudo运行，获取实际用户）
ACTUAL_USER=${SUDO_USER:-$(whoami)}
echo "当前用户: $ACTUAL_USER"

# 方案1: 将用户添加到disk组
echo "方案1: 将用户 $ACTUAL_USER 添加到 disk 组..."
usermod -a -G disk $ACTUAL_USER
if [ $? -eq 0 ]; then
    echo "✓ 成功将用户添加到disk组"
else
    echo "✗ 添加用户到disk组失败"
fi

# 方案2: 配置sudo无密码运行fio
echo "方案2: 配置sudo无密码运行fio..."
FIO_PATH=$(which fio)
if [ -z "$FIO_PATH" ]; then
    echo "警告: 未找到fio命令，请先安装fio"
    FIO_PATH="/usr/bin/fio"
fi

# 创建sudoers配置文件
SUDOERS_FILE="/etc/sudoers.d/fio-web-controller"
echo "# Allow $ACTUAL_USER to run fio without password" > $SUDOERS_FILE
echo "$ACTUAL_USER ALL=(ALL) NOPASSWD: $FIO_PATH" >> $SUDOERS_FILE

# 设置正确的权限
chmod 440 $SUDOERS_FILE
if [ $? -eq 0 ]; then
    echo "✓ 成功配置sudo无密码运行fio"
else
    echo "✗ 配置sudo失败"
fi

# 验证sudoers文件语法
visudo -c -f $SUDOERS_FILE
if [ $? -eq 0 ]; then
    echo "✓ sudoers配置文件语法正确"
else
    echo "✗ sudoers配置文件语法错误，删除配置文件"
    rm -f $SUDOERS_FILE
fi

echo
echo "=== 设置完成 ==="
echo "请注意:"
echo "1. 如果使用方案1（disk组），用户需要重新登录才能生效"
echo "2. 方案2（sudo无密码）立即生效"
echo "3. 建议重启终端或重新登录以确保权限生效"
echo
echo "测试权限:"
echo "  sudo -u $ACTUAL_USER sudo fio --version"
echo
