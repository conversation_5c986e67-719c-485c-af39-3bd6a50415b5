#!/bin/bash

# 权限测试脚本

echo "=== FIO 权限测试 ==="
echo

# 测试1: 检查用户组
echo "1. 检查用户组:"
groups | grep -q disk && echo "✓ 用户在disk组中" || echo "✗ 用户不在disk组中"

# 测试2: 检查sudo配置
echo "2. 检查sudo配置:"
if sudo -n fio --version >/dev/null 2>&1; then
    echo "✓ sudo fio 无密码运行正常"
else
    echo "✗ sudo fio 需要密码或配置错误"
fi

# 测试3: 检查设备访问
echo "3. 检查设备访问:"
if [ -b "/dev/nvme0n1" ]; then
    echo "✓ 设备 /dev/nvme0n1 存在"
    ls -la /dev/nvme0n1
    
    # 测试读取权限
    echo "4. 测试设备读取权限:"
    if sudo fio --name=test --filename=/dev/nvme0n1 --direct=1 --ioengine=libaio --rw=read --bs=4k --runtime=1 --time_based --numjobs=1 --iodepth=1 >/dev/null 2>&1; then
        echo "✓ 设备读取权限正常"
    else
        echo "✗ 设备读取权限失败"
    fi
else
    echo "✗ 设备 /dev/nvme0n1 不存在"
fi

echo
echo "=== 测试完成 ==="
